//
//  BackgroundCheckController.swift
//  
//
//  Created by <PERSON> on 9/9/25.
//

import Foundation
import Fluent
import Vapor

struct BackgroundCheckController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let backgroundChecks = routes.grouped("api", "background-checks")
        
        // Admin-only routes with token authentication
        let adminProtected = backgroundChecks.grouped(Token.authenticator())
        
        // Trigger background check for a member
        adminProtected.post("members", ":memberID", "trigger", use: triggerBackgroundCheck)
        
        // Get review queue
        adminProtected.get("queue", use: getReviewQueue)
        
        // Get specific queue item
        adminProtected.get("queue", ":queueID", use: getQueueItem)

        // Location-based search
        adminProtected.post("location-search", use: locationSearch)

        // Approve background check
        adminProtected.post("queue", ":queueID", "approve", use: approveBackgroundCheck)

        // Reject background check
        adminProtected.post("queue", ":queueID", "reject", use: rejectBackgroundCheck)
    }
    
    // MARK: - Trigger Background Check
    func triggerBackgroundCheck(req: Request) throws -> EventLoopFuture<BackgroundCheckTriggerResponse> {
        // Get member ID from parameters
        guard let memberIDString = req.parameters.get("memberID"),
              let memberID = UUID(uuidString: memberIDString) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        // Decode and validate request body
        let input = try req.content.decode(BackgroundCheckTriggerInput.self)
        try BackgroundCheckTriggerInput.validate(content: req)
        
        // Get current user and verify admin access
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                // Find the member
                return Member.find(memberID, on: req.db).flatMap { member in
                    guard let member = member else {
                        return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Member not found"))
                    }
                    
                    // Check if there's already a pending background check for this member
                    return BackgroundCheckReviewQueue.query(on: req.db)
                        .filter(\.$member.$id == memberID)
                        .filter(\.$reviewStatus == .pending)
                        .first()
                        .flatMap { existingPendingCheck in
                            if existingPendingCheck != nil {
                                return req.eventLoop.makeFailedFuture(Abort(.conflict, reason: "A background check is already pending for this member"))
                            }
                            
                            // Call Zyla API (simplified for EventLoopFuture pattern)
                            return self.performBackgroundCheck(
                                req: req,
                                member: member,
                                currentUser: currentUser,
                                input: input
                            )
                        }
                }
            }
        }
    }
    
    // MARK: - Get Review Queue
    func getReviewQueue(req: Request) throws -> EventLoopFuture<Page<BackgroundCheckQueueItem>> {
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                let query = BackgroundCheckReviewQueue.query(on: req.db)
                    .filter(\.$reviewStatus == .pending)
                    .with(\.$member)
                    .with(\.$triggeredBy)
                    .sort(\.$createdAt, .descending)
                
                return query.paginate(for: req).map { page in
                    let items = page.items.map { queueItem in
                        BackgroundCheckQueueItem(
                            id: queueItem.id!,
                            member: MemberSummary(
                                id: queueItem.member.id!,
                                firstName: queueItem.member.firstName,
                                lastName: queueItem.member.lastName,
                                email: queueItem.member.email
                            ),
                            triggeredBy: UserSummary(
                                id: queueItem.triggeredBy.id!,
                                firstName: queueItem.triggeredBy.firstName,
                                lastName: queueItem.triggeredBy.lastName,
                                email: queueItem.triggeredBy.email
                            ),
                            fullName: queueItem.fullName,
                            zipCode: queueItem.zipCode,
                            matchedCount: queueItem.matchedCount,
                            highestRiskLevel: queueItem.highestRiskLevel,
                            createdAt: queueItem.createdAt!,
                            rawResponse: queueItem.rawResponse
                        )
                    }
                    
                    return Page(
                        items: items,
                        metadata: page.metadata
                    )
                }
            }
        }
    }
    
    // MARK: - Get Queue Item
    func getQueueItem(req: Request) throws -> EventLoopFuture<BackgroundCheckQueueItem> {
        guard let queueIDString = req.parameters.get("queueID"),
              let queueID = UUID(uuidString: queueIDString) else {
            throw Abort(.badRequest, reason: "Invalid queue item ID")
        }
        
        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                return BackgroundCheckReviewQueue.find(queueID, on: req.db).flatMap { queueItem in
                    guard let queueItem = queueItem else {
                        return req.eventLoop.future(error: Abort(.notFound, reason: "Queue item not found"))
                    }
                    
                    return queueItem.$member.load(on: req.db).flatMap { _ in
                        return queueItem.$triggeredBy.load(on: req.db).map { _ in
                            req.logger.info("Queue item rawResponse: \(queueItem.rawResponse)")
                            req.logger.info("Queue item rawResponse results count: \(queueItem.rawResponse.results.count)")

                            let response = BackgroundCheckQueueItem(
                                id: queueItem.id!,
                                member: MemberSummary(
                                    id: queueItem.member.id!,
                                    firstName: queueItem.member.firstName,
                                    lastName: queueItem.member.lastName,
                                    email: queueItem.member.email
                                ),
                                triggeredBy: UserSummary(
                                    id: queueItem.triggeredBy.id!,
                                    firstName: queueItem.triggeredBy.firstName,
                                    lastName: queueItem.triggeredBy.lastName,
                                    email: queueItem.triggeredBy.email
                                ),
                                fullName: queueItem.fullName,
                                zipCode: queueItem.zipCode,
                                matchedCount: queueItem.matchedCount,
                                highestRiskLevel: queueItem.highestRiskLevel,
                                createdAt: queueItem.createdAt!,
                                rawResponse: queueItem.rawResponse
                            )

                            req.logger.info("Returning response with rawResponse: \(response.rawResponse)")
                            return response
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func verifyAdminAccess(req: Request, user: User) -> EventLoopFuture<Void> {
        return req.eventLoop.future().flatMapThrowing { _ in
            // Check if user has admin role
            guard let roles = user.roles,
                  roles.contains("drop_all") ||
                  roles.contains(where: { $0.localizedCaseInsensitiveContains("admin") })
            else {
                req.logger.error("verifyAdminAccess - Admin access required: user roles = \(user.roles ?? [])")
                throw Abort(.forbidden, reason: "Admin access required")
            }
            
            req.logger.info("verifyAdminAccess - Success for user \(user.email)")
            return ()
        }
    }
    
    let mockRecord1 = ZylaOffenderRecord(
        name: "Richard Robert Dehuff",
        aliases: "",
        address: "1600 Block Of Belmont Ave",
        city: "Seattle",
        state: "WA",
        zipCode: "98122",
        location: "47.616053,-122.32486",
        riskLevel: "Level III",
        gender: "Male",
        age: "60",
        eyeColor: "Blue",
        hairColor: "Brown",
        height: "6'02\"",
        weight: "218 lbs.",
        marksScarsTattoos: "scar on arm (lf arm)",
        race: "White",
        courtRecord: "Crime: 9A.44.073 - Rape of a child in the first degree, Conviction date: 1993-11-12, Statute: 9A.44.073, Jurisdiction: Washington | Crime: 9.68A.090 - Communication with minor for immoral purposes, Conviction date: 1993-11-12, Statute: 9.68A.090, Jurisdiction: Washington",
        photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=WA820779",
        updateDatetime: "2023-05-16T04:00:12Z"
    )
    
    let mockRecord2 = ZylaOffenderRecord(
        name: "Michael James Thompson",
        aliases: "Mike Thompson, M.J. Thompson",
        address: "2400 Block Of Pine Street",
        city: "Portland",
        state: "OR",
        zipCode: "97205",
        location: "45.523064,-122.676483",
        riskLevel: "Level II",
        gender: "Male",
        age: "45",
        eyeColor: "Brown",
        hairColor: "Black",
        height: "5'10\"",
        weight: "185 lbs.",
        marksScarsTattoos: "tattoo on right shoulder (eagle)",
        race: "White",
        courtRecord: "Crime: 163.375 - Rape in the second degree, Conviction date: 2008-03-15, Statute: 163.375, Jurisdiction: Oregon | Crime: 163.427 - Sexual abuse in the first degree, Conviction date: 2008-03-15, Statute: 163.427, Jurisdiction: Oregon",
        photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=OR445821",
        updateDatetime: "2024-01-22T08:30:45Z"
    )
    
    let mockRecord3 = ZylaOffenderRecord(
        name: "David Anthony Rodriguez",
        aliases: nil,
        address: "800 Block Of Market Street",
        city: "San Francisco",
        state: "CA",
        zipCode: "94102",
        location: "37.774929,-122.419416",
        riskLevel: "Level I",
        gender: "Male",
        age: "38",
        eyeColor: "Hazel",
        hairColor: "Brown",
        height: "5'08\"",
        weight: "165 lbs.",
        marksScarsTattoos: nil,
        race: "Hispanic",
        courtRecord: "Crime: PC 288(a) - Lewd acts with a child under 14, Conviction date: 2015-09-08, Statute: PC 288(a), Jurisdiction: California",
        photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=CA992847",
        updateDatetime: "2024-08-10T14:15:30Z"
    )
    
    let mockRecord4 = ZylaOffenderRecord(
        name: "Sarah Elizabeth Williams",
        aliases: "Sarah E. Williams, Beth Williams",
        address: "1200 Block Of Broadway",
        city: "Denver",
        state: "CO",
        zipCode: "80203",
        location: "39.739236,-104.990251",
        riskLevel: "Level II",
        gender: "Female",
        age: "42",
        eyeColor: "Green",
        hairColor: "Blonde",
        height: "5'06\"",
        weight: "140 lbs.",
        marksScarsTattoos: "scar on left cheek",
        race: "White",
        courtRecord: "Crime: 18-3-405 - Sexual assault on a child, Conviction date: 2012-06-20, Statute: 18-3-405, Jurisdiction: Colorado",
        photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=CO334567",
        updateDatetime: "2023-12-05T11:45:22Z"
    )
    
    let mockRecord5 = ZylaOffenderRecord(
        name: "Robert Charles Anderson",
        aliases: "Bob Anderson, R.C. Anderson",
        address: "3500 Block Of Oak Avenue",
        city: "Phoenix",
        state: "AZ",
        zipCode: "85018",
        location: "33.448457,-112.073844",
        riskLevel: "Level III",
        gender: "Male",
        age: "67",
        eyeColor: "Blue",
        hairColor: "Gray",
        height: "6'01\"",
        weight: "195 lbs.",
        marksScarsTattoos: "multiple tattoos on arms, scar on forehead",
        race: "White",
        courtRecord: "Crime: ARS 13-1405 - Sexual conduct with a minor, Conviction date: 1998-11-30, Statute: ARS 13-1405, Jurisdiction: Arizona | Crime: ARS 13-1410 - Molestation of a child, Conviction date: 1998-11-30, Statute: ARS 13-1410, Jurisdiction: Arizona | Crime: ARS 13-1405 - Sexual conduct with a minor, Conviction date: 2019-04-12, Statute: ARS 13-1405, Jurisdiction: Arizona",
        photoUrl: "https://photo.familywatchdog.us/OffenderPhoto/OffenderPhoto.aspx?id=AZ778901",
        updateDatetime: "2024-03-18T16:20:15Z"
    )
    
    private func performBackgroundCheck(
        req: Request,
        member: Member,
        currentUser: User,
        input: BackgroundCheckTriggerInput
    ) -> EventLoopFuture<BackgroundCheckTriggerResponse> {
        // For now, simulate a background check response
        // In production, this would call the Zyla API
        let mockSearchQuery = ZylaSearchQuery(name: input.fullName, zipCode: input.zipCode)
        
        let mockZylaResponse = ZylaOffenderResponse(
            results: [mockRecord1, mockRecord2, mockRecord3, mockRecord4, mockRecord5],
            searchQuery: mockSearchQuery,
            timestamp: Date()
        )
        
        let matchedCount = mockZylaResponse.results.count
        
        if matchedCount > 0 {
            // Create queue item for review
            let queueItem = BackgroundCheckReviewQueue(
                memberId: try! member.requireID(),
                triggeredById: try! currentUser.requireID(),
                fullName: input.fullName,
                zipCode: input.zipCode,
                rawResponse: mockZylaResponse,
                matchedCount: matchedCount,
                requiresReview: true,
                reviewStatus: .pending
            )
            
            return queueItem.save(on: req.db).flatMap { _ in
                return self.createTimelineItem(
                    req: req,
                    memberId: try! member.requireID(),
                    creatorId: try! currentUser.requireID(),
                    title: "Background Check – Match Found",
                    description: "Background check found \(matchedCount) potential match(es). Review required.",
                    status: "background_check_match",
                    visible: false
                ).map { _ in
                    return BackgroundCheckTriggerResponse(
                        success: true,
                        message: "Background check completed. \(matchedCount) match(es) found - review required.",
                        matchesFound: matchedCount,
                        requiresReview: true,
                        queueItemId: queueItem.id
                    )
                }
            }
        } else {
            // No matches found - create clear background check entry directly
            let backgroundCheck = BackgroundCheck(
                status: "clear",
                matchType: "no_match",
                reviewStatus: "approved",
                reviewedBy: try! currentUser.requireID(),
                requiresReview: false
            )
            
            member.addBackgroundCheck(backgroundCheck)
            
            return member.save(on: req.db).flatMap { _ in
                return self.createTimelineItem(
                    req: req,
                    memberId: try! member.requireID(),
                    creatorId: try! currentUser.requireID(),
                    title: "Background Check Cleared",
                    description: "Background check completed with no matches found.",
                    status: "background_check_clear",
                    visible: true
                ).map { _ in
                    return BackgroundCheckTriggerResponse(
                        success: true,
                        message: "Background check completed. No matches found.",
                        matchesFound: 0,
                        requiresReview: false,
                        queueItemId: nil
                    )
                }
            }
        }
    }

    // MARK: - Location-Based Search
    func locationSearch(req: Request) throws -> EventLoopFuture<LocationSearchResponse> {
        req.logger.info("Location search endpoint called")

        let input: LocationSearchInput
        do {
            input = try req.content.decode(LocationSearchInput.self)
            req.logger.info("Decoded input: memberId=\(input.memberId), address=\(input.address)")
        } catch {
            req.logger.error("Failed to decode LocationSearchInput: \(error)")
            return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Invalid request data"))
        }

        return (try? AuthController.userFromToken(req: req))?.flatMap { currentUser in
            req.logger.info("User authenticated")

            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                req.logger.info("Admin access verified")

                // Create Zyla service
                let zylaService = ZylaOffenderRegistryService(
                    client: req.application.http.client.shared,
                    apiKey: Environment.get("ZYLA_API_KEY") ?? ""
                )

                // Perform location-based search
                return req.eventLoop.makeFutureWithTask {
                    try await zylaService.searchOffendersByLocation(
                        address: input.address,
                        latitude: input.latitude,
                        longitude: input.longitude
                    )
                }.flatMap { locationResponse in
                    req.logger.info("Got location response with \(locationResponse.totalCount) total offenders")

                    // Create queue item for location-based search
                    let queueItem = BackgroundCheckReviewQueue(
                        memberId: input.memberId, // Use the actual memberId from input
                        triggeredById: try! currentUser.requireID(),
                        fullName: "Location Search: \(input.address)",
                        zipCode: self.extractZipCode(from: input.address) ?? "Unknown",
                        rawResponse: self.convertLocationToStandardResponse(locationResponse),
                        matchedCount: locationResponse.totalCount,
                        highestRiskLevel: self.determineHighestRiskLevel(from: locationResponse),
                        requiresReview: locationResponse.totalCount > 0
                    )

                    return queueItem.save(on: req.db).map { _ in
                        return LocationSearchResponse(
                            queueId: queueItem.id!,
                            exactMatches: locationResponse.exactMatches,
                            nearbyOffenders: locationResponse.nearbyOffenders,
                            totalCount: locationResponse.totalCount,
                            searchQuery: locationResponse.searchQuery
                        )
                    }
                }
            }
        } ?? req.eventLoop.makeFailedFuture(Abort(.unauthorized, reason: "Authentication failed"))
    }

    // MARK: - Location Search Helper Methods
    private func extractZipCode(from address: String) -> String? {
        let zipPattern = "\\b\\d{5}\\b"
        let regex = try? NSRegularExpression(pattern: zipPattern)
        let range = NSRange(address.startIndex..<address.endIndex, in: address)

        if let match = regex?.firstMatch(in: address, range: range) {
            return String(address[Range(match.range, in: address)!])
        }

        return nil
    }

    private func convertLocationToStandardResponse(_ locationResponse: ZylaLocationResponse) -> ZylaOffenderResponse {
        // Combine exact matches and nearby offenders into a single results array
        let allOffenders = locationResponse.exactMatches + locationResponse.nearbyOffenders

        // Create a standard search query from location query
        let standardQuery = ZylaSearchQuery(
            name: "Location Search",
            zipCode: extractZipCode(from: locationResponse.searchQuery.address) ?? "Unknown"
        )

        return ZylaOffenderResponse(
            results: allOffenders,
            searchQuery: standardQuery,
            timestamp: locationResponse.timestamp
        )
    }

    private func determineHighestRiskLevel(from locationResponse: ZylaLocationResponse) -> String? {
        let allOffenders = locationResponse.exactMatches + locationResponse.nearbyOffenders
        let riskLevels = allOffenders.compactMap { $0.riskLevel }

        // Priority: Level III > Level II > Level I
        if riskLevels.contains(where: { $0.contains("III") }) {
            return "Level III"
        } else if riskLevels.contains(where: { $0.contains("II") }) {
            return "Level II"
        } else if riskLevels.contains(where: { $0.contains("I") }) {
            return "Level I"
        }

        return riskLevels.first
    }

    // MARK: - Approve Background Check
    func approveBackgroundCheck(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let queueIDString = req.parameters.get("queueID"),
              let queueID = UUID(uuidString: queueIDString) else {
            throw Abort(.badRequest, reason: "Invalid queue item ID")
        }

        let input = try req.content.decode(BackgroundCheckReviewInput.self)

        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                return BackgroundCheckReviewQueue.find(queueID, on: req.db).flatMap { queueItem in
                    guard let queueItem = queueItem else {
                        return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Queue item not found"))
                    }

                    guard queueItem.reviewStatus == .pending else {
                        return req.eventLoop.future(error: Abort(.conflict, reason: "Queue item has already been reviewed"))
                    }

                    // Validate that an offender was selected
                    guard let selectedIndex = input.selectedOffenderIndex else {
                        return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "No offender selected for approval"))
                    }

                    guard selectedIndex >= 0 && selectedIndex < queueItem.rawResponse.results.count else {
                        return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Invalid offender selection"))
                    }

                    // Update queue item
                    queueItem.reviewStatus = .approved
                    queueItem.$reviewedBy.id = try! currentUser.requireID()
                    queueItem.reviewedAt = Date()
                    queueItem.reviewNotes = input.notes
                    queueItem.selectedOffenderIndex = selectedIndex
                    queueItem.matchSelections = input.matchSelections

                    return queueItem.save(on: req.db).flatMap { _ in
                        return queueItem.$member.load(on: req.db).flatMap { _ in
                            // Get only the selected offender record
                            let selectedOffender = queueItem.rawResponse.results[selectedIndex]

                            // Add to member's security profile using only the selected offender
                            let backgroundCheck = BackgroundCheck(
                                status: "flagged",
                                matchType: "verified_match",
                                reviewStatus: "approved",
                                reviewedBy: try! currentUser.requireID(),
                                requiresReview: false
                            )

                            queueItem.member.addBackgroundCheck(backgroundCheck)

                            return queueItem.member.save(on: req.db).flatMap { _ in
                                // Create timeline item
                                return self.createTimelineItem(
                                    req: req,
                                    memberId: try! queueItem.member.requireID(),
                                    creatorId: try! currentUser.requireID(),
                                    title: "Background Check Verified",
                                    description: "Background check approved by admin. Selected match: \(selectedOffender.name). Security flags applied.",
                                    status: "background_check_approved",
                                    visible: true
                                ).flatMap { _ in
                                    // Apply security tags
                                    return self.applySecurityTags(
                                        req: req,
                                        member: queueItem.member,
                                        queueItem: queueItem,
                                        currentUser: currentUser
                                    ).map { _ in
                                        return .ok
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // MARK: - Reject Background Check
    func rejectBackgroundCheck(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let queueIDString = req.parameters.get("queueID"),
              let queueID = UUID(uuidString: queueIDString) else {
            throw Abort(.badRequest, reason: "Invalid queue item ID")
        }

        let input = try req.content.decode(BackgroundCheckReviewInput.self)

        return try AuthController.userFromToken(req: req).flatMap { currentUser in
            return self.verifyAdminAccess(req: req, user: currentUser).flatMap { _ in
                return BackgroundCheckReviewQueue.find(queueID, on: req.db).flatMap { queueItem in
                    guard let queueItem = queueItem else {
                        return req.eventLoop.makeFailedFuture(Abort(.notFound, reason: "Queue item not found")) as EventLoopFuture<HTTPStatus>
                    }

                    guard queueItem.reviewStatus == .pending else {
                        return req.eventLoop.future(error: Abort(.conflict, reason: "Queue item has already been reviewed"))
                    }

                    // Update queue item
                    queueItem.reviewStatus = .rejected
                    queueItem.$reviewedBy.id = try! currentUser.requireID()
                    queueItem.reviewedAt = Date()
                    queueItem.reviewNotes = input.notes
                    queueItem.selectedOffenderIndex = input.selectedOffenderIndex
                    queueItem.matchSelections = input.matchSelections

                    return queueItem.save(on: req.db).flatMap { _ in
                        return queueItem.$member.load(on: req.db).flatMap { _ in
                            // Add clear background check to member's security profile
                            let backgroundCheck = BackgroundCheck(
                                status: "clear",
                                matchType: "false_positive",
                                reviewStatus: "rejected",
                                reviewedBy: try! currentUser.requireID(),
                                requiresReview: false
                            )

                            queueItem.member.addBackgroundCheck(backgroundCheck)

                            return queueItem.member.save(on: req.db).flatMap { _ in
                                // Create timeline item
                                return self.createTimelineItem(
                                    req: req,
                                    memberId: try! queueItem.member.requireID(),
                                    creatorId: try! currentUser.requireID(),
                                    title: "Background Check Cleared",
                                    description: "Background check rejected by admin. No security concerns.",
                                    status: "background_check_rejected",
                                    visible: false
                                ).map { _ in
                                    return .ok
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private func createTimelineItem(
        req: Request,
        memberId: UUID,
        creatorId: UUID,
        title: String,
        description: String,
        status: String,
        visible: Bool
    ) -> EventLoopFuture<Void> {
        let timelineItem = TimeLineItemMessage.general(
            memberId: memberId,
            title: title,
            desc: description,
            status: status,
            visible: visible,
            meta: nil
        ).toTimelineItem()

        return try! TimelineControllerController.create(
            [timelineItem],
            creatorId: creatorId,
            req: req
        ).transform(to: ())
    }

    private func applySecurityTags(
        req: Request,
        member: Member,
        queueItem: BackgroundCheckReviewQueue,
        currentUser: User
    ) -> EventLoopFuture<Void> {
        // Create security tags based on risk level
        var tagInputs: [TagInput] = [
            TagInput(
                name: "Offender Registry Flagged",
                key: "offender_registry_flagged",
                color: "FF6B6B" // Red color for security flags
            )
        ]

        if let riskLevel = queueItem.highestRiskLevel {
            if riskLevel.contains("III") {
                tagInputs.append(TagInput(
                    name: "Risk Level III",
                    key: "risk_level_3",
                    color: "DC2626" // Dark red for highest risk
                ))
            } else if riskLevel.contains("II") {
                tagInputs.append(TagInput(
                    name: "Risk Level II",
                    key: "risk_level_2",
                    color: "F59E0B" // Orange for medium risk
                ))
            } else if riskLevel.contains("I") {
                tagInputs.append(TagInput(
                    name: "Risk Level I",
                    key: "risk_level_1",
                    color: "FCD34D" // Yellow for low risk
                ))
            }
        }

        // Convert TagInputs to Tags
        let allTags = tagInputs.compactMap { $0.tag() }
        let tagNames = allTags.compactMap { $0.name }.joined(separator: ", ")

        // Create tags and associate with member
        return member.$tags.create(allTags, on: req.db).flatMap { _ in
            // Create timeline entry for audit trail
            let timelineItem = TimeLineItemMessage.generalMemberUpdate(
                member: member,
                title: "Security Tags Applied",
                desc: "Security tags applied based on background check results: \(tagNames)",
                status: "security_tags_applied"
            ).toTimelineItem()

            return try! TimelineControllerController.create(
                [timelineItem],
                creatorId: currentUser.id!,
                req: req
            ).transform(to: ())
        }
    }
}

// MARK: - Request/Response Models
struct BackgroundCheckTriggerInput: Content, Validatable {
    let fullName: String
    let zipCode: String

    static func validations(_ validations: inout Validations) {
        validations.add("fullName", as: String.self, is: !.empty)
        validations.add("zipCode", as: String.self, is: .count(5...5))
    }
}

struct BackgroundCheckTriggerResponse: Content {
    let success: Bool
    let message: String
    let matchesFound: Int
    let requiresReview: Bool
    let queueItemId: UUID?
}

struct BackgroundCheckReviewInput: Content {
    let notes: String?
    let selectedOffenderIndex: Int?
    let matchSelections: [MatchSelection]?
}

struct BackgroundCheckQueueItem: Content {
    let id: UUID
    let member: MemberSummary
    let triggeredBy: UserSummary
    let fullName: String
    let zipCode: String
    let matchedCount: Int
    let highestRiskLevel: String?
    let createdAt: Date
    let rawResponse: ZylaOffenderResponse
}

struct MemberSummary: Content {
    let id: UUID
    let firstName: String
    let lastName: String
    let email: String
}

struct UserSummary: Content {
    let id: UUID
    let firstName: String
    let lastName: String
    let email: String
}

// MARK: - Location Search Structures
struct LocationSearchInput: Content {
    let memberId: UUID
    let address: String
    let latitude: Double
    let longitude: Double
}

struct LocationSearchResponse: Content {
    let queueId: UUID
    let exactMatches: [ZylaOffenderRecord]
    let nearbyOffenders: [ZylaOffenderRecord]
    let totalCount: Int
    let searchQuery: ZylaLocationQuery
}
